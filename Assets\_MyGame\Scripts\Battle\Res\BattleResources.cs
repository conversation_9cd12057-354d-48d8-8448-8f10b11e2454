using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using Spine.Unity;
using UnityEngine;
using UnityEngine.U2D;
using YooAsset;

public class BattleResources
{
    private static BattleResources _inst;
    public static BattleResources Inst
    {
        get
        {
            _inst ??= new BattleResources();
            return _inst;
        }
    }

    // public Texture themeTexture;
    public Sprite themeBg;
    public SpriteAtlas iconAtlas;
    public SubAssetsOperationHandle iconAtlasHandle;
    public Sprite firecracker;

    private readonly Dictionary<int, Texture> _blockTextures = new();

    async public UniTask PreLoad(Action<float> onProgress)
    {
        // 初始化进度为0
        onProgress?.Invoke(0f);

        var themeId = SystemFacade.SkinSystem.GetCurrentThemeId();
        var themeDir = $"theme{themeId}";

        // 创建所有资源加载任务（并发执行）
        var themeBgTask = LoadSpriteAsync($"Theme/{themeDir}/themeBg{themeId}");
        var firecrackerTask = LoadSpriteAsync($"Textures/firecracker");
        var iconAtlasTask = LoadSpriteAtlasAsync($"Theme/{themeDir}/theme{themeId}");

        AssetBundleManager.LoadTexture("Textures/brick1", (tex) =>
        {

        });

        AssetBundleManager.LoadAssets("Textures/brick1", (obj) =>
        {
            Log.Info("obj:" + obj);
        });

        // 创建所有纹理加载任务
        var textureLoadTasks = new List<UniTask>
        {
            LoadBlockTexture(Tile3D.TYPE_BLOCK, $"Textures/brick{themeId}"),
            LoadBlockTexture(Tile3D.TYPE_FREEZE1, $"Textures/freeze1"),
            LoadBlockTexture(Tile3D.TYPE_FREEZE2, $"Textures/freeze2"),
            LoadBlockTexture(Tile3D.TYPE_FREEZE3, $"Textures/freeze3"),
            LoadBlockTexture(Tile3D.TYPE_CHAIN, $"Textures/chain"),
            LoadBlockTexture(Tile3D.TYPE_WOOD, $"Textures/wood"),
            LoadBlockTexture(Tile3D.TYPE_STONE, $"Textures/stone")
        };

        // 合并所有资源加载任务
        var allResourceTasks = new List<UniTask>
        {
            iconAtlasTask.AsUniTask(),
            themeBgTask.AsUniTask(),
            firecrackerTask.AsUniTask()
        };
        allResourceTasks.AddRange(textureLoadTasks);

        // 同时启动对象池预制体加载任务
        var poolNames = PoolNames.GetNames();
        var poolPrefabTasks = new Dictionary<string, UniTask<GameObject>>();
        for (int i = 0; i < poolNames.Length; i++)
        {
            var poolName = poolNames[i];
            poolPrefabTasks.Add(poolName, LoadPrefabAsync(poolName));
        }

        // 总任务数：资源任务 + 对象池任务
        int totalResourceTasks = allResourceTasks.Count;
        int totalPoolTasks = poolPrefabTasks.Count;
        int totalTasks = totalResourceTasks + totalPoolTasks;

        // 使用进度追踪器来监控并发任务的完成情况
        var progressTracker = new ConcurrentProgressTracker(totalTasks, onProgress);

        // 启动所有资源加载任务的进度监控
        var resourceTasksWithProgress = allResourceTasks.Select(async (task, index) =>
        {
            await task;
            progressTracker.ReportTaskCompleted();
        }).ToArray();

        // 启动所有对象池预制体加载任务的进度监控
        var poolTasksWithProgress = poolPrefabTasks.Select(async kvp =>
        {
            await kvp.Value;
            progressTracker.ReportTaskCompleted();
        }).ToArray();

        // 等待所有资源和预制体加载完成
        await UniTask.WhenAll(resourceTasksWithProgress.Concat(poolTasksWithProgress));

        // 获取加载结果
        iconAtlasHandle = await iconAtlasTask;
        InitThemeIcon();
        themeBg = await themeBgTask;
        firecracker = await firecrackerTask;

        // 创建对象池（这部分相对较快，可以保持串行）
        await CreateObjectPools(poolPrefabTasks, progress =>
        {
            // 对象池创建进度映射到总进度的最后5%
            float totalProgress = 0.95f + (progress * 0.05f);
            onProgress?.Invoke(totalProgress);
        });

        // 报告100%进度
        onProgress?.Invoke(1f);
    }

    /// <summary>
    /// 创建对象池
    /// </summary>
    private async UniTask CreateObjectPools(Dictionary<string, UniTask<GameObject>> poolPrefabTasks, Action<float> onProgress)
    {
        PoolMgr.Inst.Clear();

        // 创建对象池任务列表
        var poolCreationTasks = new List<Action>
        {
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_Explosion].GetAwaiter().GetResult(), PoolNames.Pool_Explosion, 2, 10, true),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectIceClick].GetAwaiter().GetResult(), PoolNames.Pool_EffectIceClick, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectIceCrush].GetAwaiter().GetResult(), PoolNames.Pool_EffectIceCrush, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectVineClick].GetAwaiter().GetResult(), PoolNames.Pool_EffectVineClick, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectVineCrush].GetAwaiter().GetResult(), PoolNames.Pool_EffectVineCrush, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectWoodClick].GetAwaiter().GetResult(), PoolNames.Pool_EffectWoodClick, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectStoneClick].GetAwaiter().GetResult(), PoolNames.Pool_EffectStoneClick, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_Firecracker].GetAwaiter().GetResult(), PoolNames.Pool_Firecracker, 6, 12),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectFireworksBoom].GetAwaiter().GetResult(), PoolNames.Pool_EffectFireworksBoom, 8, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectTileSelected].GetAwaiter().GetResult(), PoolNames.Pool_EffectTileSelected, 2, 10),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectFlash].GetAwaiter().GetResult(), PoolNames.Pool_EffectFlash, 4, 8),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectFlashPoint].GetAwaiter().GetResult(), PoolNames.Pool_EffectFlashPoint, 4, 8),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectFlashTurn].GetAwaiter().GetResult(), PoolNames.Pool_EffectFlashTurn, 4, 8),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectShinePink].GetAwaiter().GetResult(), PoolNames.Pool_EffectShinePink, 2, 6),
            () => PoolMgr.Inst.CreatePool(poolPrefabTasks[PoolNames.Pool_EffectTileBroken].GetAwaiter().GetResult(), PoolNames.Pool_EffectTileBroken, 2, 10)
        };

        // 逐个创建对象池并报告进度
        for (int i = 0; i < poolCreationTasks.Count; i++)
        {
            poolCreationTasks[i]();

            // 计算进度
            float progress = (float)(i + 1) / poolCreationTasks.Count;
            onProgress?.Invoke(progress);

            // 让出一帧，避免卡顿
            await UniTask.Yield();
        }
    }

    private readonly Dictionary<int, Sprite> _themeIcons = new();
    private void InitThemeIcon()
    {
        var allAssetObjects = iconAtlasHandle.GetSubAssetObjects<Sprite>();
        var index = 0;
        foreach (var assetObject in allAssetObjects)
        {
            _themeIcons[index] = assetObject;
            index++;
        }
    }

    public Sprite GetThemeIcon(int iconIndex)
    {
        return _themeIcons[iconIndex];
    }



    public PoolItem CreateExplosion() { return PoolMgr.Inst.Get(PoolNames.Pool_Explosion); }
    // public CircleNum CreateCircleNum() { return PoolMgr.Inst.Get<CircleNum>(PoolNames.Pool_CircleNum); }
    public PoolItem CreateFirecracker() { return PoolMgr.Inst.Get(PoolNames.Pool_Firecracker); }
    public PoolItem CreateFirecrackerExplosion() { return PoolMgr.Inst.Get(PoolNames.Pool_EffectFireworksBoom); }
    public PoolItem GetPoolItem(string poolName)
    {
        return PoolMgr.Inst.Get(poolName);
    }

    public void Dispose()
    {
        // 清理对象池
        PoolMgr.Inst.Clear();

        // 清理纹理引用
        _blockTextures.Clear();
        // themeTexture = null;
        themeBg = null;
    }

    public async UniTask LoadBlockTexture(int type, string path)
    {
        _blockTextures[type] = await LoadTextureAsync(path);
    }
    public Texture GetBlockTexture(int type)
    {
        if (_blockTextures.TryGetValue(type, out var texture))
        {
            return texture;
        }
        return null;
    }

    private UniTask<GameObject> LoadPrefabAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<GameObject>();
        AssetBundleManager.LoadPrefab(path, (tex) => tcs.TrySetResult(tex));
        return tcs.Task;
    }

    private UniTask<Texture> LoadTextureAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<Texture>();
        AssetBundleManager.LoadTexture(path, (tex) => tcs.TrySetResult(tex));
        return tcs.Task;
    }

    private UniTask<Sprite> LoadSpriteAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<Sprite>();
        AssetBundleManager.LoadSprite(path, (sprite) => tcs.TrySetResult(sprite));
        return tcs.Task;
    }

    private UniTask<SubAssetsOperationHandle> LoadSpriteAtlasAsync(string path)
    {
        var tcs = new UniTaskCompletionSource<SubAssetsOperationHandle>();
        AssetBundleManager.LoadSpriteAtlas(path, (sprite) => tcs.TrySetResult(sprite));
        return tcs.Task;
    }
}

/// <summary>
/// 并发进度追踪器，用于监控多个并发任务的完成进度
/// </summary>
public class ConcurrentProgressTracker
{
    private readonly int _totalTasks;
    private readonly Action<float> _onProgress;
    private int _completedTasks;
    private readonly object _lock = new();

    public ConcurrentProgressTracker(int totalTasks, Action<float> onProgress)
    {
        _totalTasks = totalTasks;
        _onProgress = onProgress;
        _completedTasks = 0;
    }

    public void ReportTaskCompleted()
    {
        lock (_lock)
        {
            _completedTasks++;
            float progress = (float)_completedTasks / _totalTasks * 0.95f; // 保留5%给对象池创建
            _onProgress?.Invoke(progress);
        }
    }
}
